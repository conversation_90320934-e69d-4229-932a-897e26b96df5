import { type NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClientOnRequest } from '@/lib/supabase/server';
import { z } from 'zod';

// Define the browsing model schema
const BrowsingModelSchema = z.object({
  id: z.string(),
  provider: z.string(),
  model: z.string(),
  temperature: z.number().min(0).max(2).optional(),
  order: z.number().min(0)
});

const UpdateBrowsingConfigSchema = z.object({
  browsing_enabled: z.boolean(),
  browsing_models: z.array(BrowsingModelSchema)
});

interface RouteParams {
  params: Promise<{
    configId: string;
  }>;
}

export async function PUT(request: NextRequest, { params }: RouteParams) {
  const { configId } = await params;

  if (!configId) {
    return NextResponse.json({ error: 'Configuration ID is required' }, { status: 400 });
  }

  try {
    const body = await request.json();
    const validatedData = UpdateBrowsingConfigSchema.parse(body);

    const supabase = createSupabaseServerClientOnRequest(request);

    // Update the custom_api_configs table with browsing configuration
    const { data, error } = await supabase
      .from('custom_api_configs')
      .update({
        browsing_enabled: validatedData.browsing_enabled,
        browsing_models: validatedData.browsing_models,
        updated_at: new Date().toISOString()
      })
      .eq('id', configId)
      .select()
      .single();

    if (error) {
      console.error('Supabase error updating browsing config:', error);
      return NextResponse.json(
        { error: 'Failed to update browsing configuration', details: error.message },
        { status: 500 }
      );
    }

    if (!data) {
      return NextResponse.json(
        { error: 'Configuration not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      message: 'Browsing configuration updated successfully',
      config: data
    });

  } catch (error: any) {
    console.error('Error updating browsing configuration:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error', details: error.message },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  const { configId } = await params;

  if (!configId) {
    return NextResponse.json({ error: 'Configuration ID is required' }, { status: 400 });
  }

  try {
    const supabase = createSupabaseServerClientOnRequest(request);

    // Get the browsing configuration for this config
    const { data, error } = await supabase
      .from('custom_api_configs')
      .select('id, name, browsing_enabled, browsing_models')
      .eq('id', configId)
      .single();

    if (error) {
      console.error('Supabase error fetching browsing config:', error);
      return NextResponse.json(
        { error: 'Failed to fetch browsing configuration', details: error.message },
        { status: 500 }
      );
    }

    if (!data) {
      return NextResponse.json(
        { error: 'Configuration not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      browsing_enabled: data.browsing_enabled || false,
      browsing_models: data.browsing_models || []
    });

  } catch (error: any) {
    console.error('Error fetching browsing configuration:', error);
    return NextResponse.json(
      { error: 'Internal server error', details: error.message },
      { status: 500 }
    );
  }
}
